#!/usr/bin/env bash

set -ex

KUBE_NAMESPACE=${KUBE_NAMESPACE:-"s3-data-loader"}
CI_REGISTRY_IMAGE=${CI_REGISTRY_IMAGE:-"registry.gitlab.com/coinmetrics/data-delivery/s3-data-loaders/order-books"}
CI_COMMIT_SHA=${CI_COMMIT_SHA:-$(git rev-parse origin/$(git rev-parse --abbrev-ref HEAD))}

DL_DB_THREADS=${DL_DB_THREADS:-"1"}
DL_HEAP_SIZE=${DL_HEAP_SIZE:-"1G"}
DL_MEM_SIZE=${DL_MEM_SIZE:-"2Gi"}
DL_CPU=${DL_CPU:-"2"}
DL_TMP_VOLUME_SIZE=${DL_TMP_VOLUME_SIZE:-"16Gi"}

DL_USE_NEW_BOOKS_TABLES=${DL_USE_NEW_BOOKS_TABLES,:-"false"}

cat <<EOF | kubectl --context cp1 apply -n $KUBE_NAMESPACE --force -f - || exit 1
---
apiVersion: batch/v1
kind: Job
metadata:
  name: "${DL_JOB,,}-order-book-s3-data-loader"
spec:
  backoffLimit: 0
  template:
    metadata:
      labels:
        app.kubernetes.io/name: "s3-data-loader"
    spec:
      containers:
        - name: main
          # it can be used to debug container's fs
          #command:
          #  - sh
          #  - -c
          #  - while true; do sleep 1000; done
          image: "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
          env:
            - name: DL_JOB
              value: "$DL_JOB"
            - name: DL_DB_BOOKS
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-books-partitioned-1-p?user=postgres&password=\$(PGPASSWORD)"
            - name: DL_USE_NEW_BOOKS_TABLES
              value: "$DL_USE_NEW_BOOKS_TABLES"
            - name: DL_DB_THREADS
              value: "$DL_DB_THREADS"
            - name: DL_START_DATE
              value: "$DL_START_DATE"
            - name: DL_END_DATE
              value: "$DL_END_DATE"
            - name: DL_EXCHANGE
              value: "$DL_EXCHANGE"
            - name: DL_HEAP_SIZE
              value: "$DL_HEAP_SIZE"
            - name: DL_JVM_CPU
              value: "$DL_CPU"
            - name: DL_S3_ENDPOINT
              value: "https://s3-us-east-1.coinmetrics.io"
            - name: DL_S3_REGION
              value: "us-east-1"
            - name: DL_S3_ACCESS_KEY
              value: "JKkn232ob23knnKba3"
            - name: DL_S3_SECRET_KEY
              value: "\$(S3_SECRET_KEY)"
            - name: DL_PLAIN_LOG_LEVEL
              value: "off"
            - name: DL_JSON_LOG_LEVEL
              value: "trace"
          envFrom:
            - secretRef:
                name: secret-db
                optional: false
          volumeMounts:
            - mountPath: "/tmp"
              name: temp-volume
          resources:
            limits:
              memory: "$DL_MEM_SIZE"
            requests:
              cpu: "$DL_CPU"
              memory: "$DL_MEM_SIZE"
          ports:
            - name: healthcheck
              containerPort: 8080
            - name: monitoring
              containerPort: 8081
      restartPolicy: Never
      # it's needed for correct /tmp volume rights
      securityContext:
        fsGroup: 1000
      volumes:
        - name: temp-volume
          emptyDir:
            sizeLimit: "$DL_TMP_VOLUME_SIZE"
EOF

exit 0
