package io.coinmetrics.s3dataloaders.orderbooks.batch.legacy

import io.coinmetrics.databases.DbConfig
import io.coinmetrics.databases.DbMonitoring
import io.coinmetrics.databases.toDatabase
import io.coinmetrics.s3databases.S3Databases
import io.coinmetrics.s3databases.storageclients.StorageClient
import io.coinmetrics.s3dataloaders.orderbooks.ApiBookEntry
import io.coinmetrics.s3dataloaders.orderbooks.ApiMarketOrderBook
import io.coinmetrics.s3dataloaders.orderbooks.BookDepth
import io.coinmetrics.s3dataloaders.orderbooks.BookMarketType
import io.coinmetrics.s3dataloaders.orderbooks.BucketNamePrefix
import io.coinmetrics.s3dataloaders.orderbooks.OrderBookUtils
import io.coinmetrics.s3dataloaders.orderbooks.OrderBookUtils.forEachInParallel
import io.coinmetrics.s3dataloaders.orderbooks.Resources
import io.coinmetrics.s3dataloaders.orderbooks.batch.CompositeJob
import io.coinmetrics.s3dataloaders.orderbooks.batch.Monitoring
import io.coinmetrics.s3dataloaders.orderbooks.batch.legacy.DerivativesBookDbReaderLegacy.DerivativesBookDataLegacy
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collect
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.system.measureTimeMillis
import kotlin.time.measureTime

/**
 * To run locally against cdev1, use:
 * kubectl --context cdev1 -n pgbouncer port-forward svc/pgbouncer 5432
 *
 * Set env variable:
 * DL_DB_BOOKS=postgresql://127.0.0.1:5432/pg-books-stg-hel1-p?user=postgres&password=<password>
 * DL_DB_THREADS=1 (default value)
 * DL_START_DATE=2023-05-20
 *
 * Heap usage per thread depends on numberOfTempFiles * TempFileWriter.bufferSize (4096) and on the DB fetch size
 * DL_HEAP_SIZE=1G (1G per thread is enough)
 * DL_MEM_SIZE=2G
 *
 * in cdev1, we have data only from some previous months
 * select book_time from futures_books order by book_time asc limit 1;
 *
 * The password can be found in Dashlane under the "Cdev1 pgbouncer" item.
 */
@Deprecated("Used for the old tables")
open class DerivativesBookDbToS3JobLegacy(
    private val marketType: BookMarketType,
    private val monitoring: Monitoring = Monitoring(),
    storageClient: StorageClient,
    private val dispatcher: CoroutineDispatcher = Dispatchers.Default,
    private val tenPercentMidPriceBookStartTime: Instant = OrderBookUtils.tenPercentMidPriceBookStartTime,
) : CompositeJob {
    private val log: Logger = LoggerFactory.getLogger("${DerivativesBookDbToS3JobLegacy::class.java}-$marketType")

    final override val supportedDataTypes = BucketNamePrefix.entries.filter { it.marketType == marketType }.toSet()
    private val tenPercentMidPriceBookBookDataTypes =
        supportedDataTypes.filter { it.depth == BookDepth.DEPTH_100 || it.depth == BookDepth.DEPTH_10PCT }
    private val depth100BookDataType = supportedDataTypes.first { it.depth == BookDepth.DEPTH_100 }
    private val depth10PctBookDataType = supportedDataTypes.first { it.depth == BookDepth.DEPTH_10PCT }
    private val depthFullBookDataType = supportedDataTypes.first { it.depth == BookDepth.DEPTH_FULL }

    private val dbMonitoring =
        object : DbMonitoring {
            override fun observeExecutionQueueDuration(
                dbName: String,
                durationMs: Long,
            ) {
                // no need to monitor it because we don't have a queue when the number of db threads = the number of app threads
                // log.debug("SQL query spent {} ms in a queue.", durationMs)
            }

            override fun observeQueryDuration(
                dbName: String,
                tableName: String,
                durationMs: Long,
            ) {
                log.trace("SQL query itself took {} ms.", durationMs)
            }

            override fun setExecutionQueueLength(
                dbName: String,
                value: Int,
            ) {
                // no need to monitor it because we don't have a queue when the number of db threads = the number of app threads
                // log.debug("Pending SQL queries in the queue: {}.", value)
            }
        }

    open fun getDbConfig(): DbConfig =
        DbConfig(
            appName = "DL",
            dbConfigName = "BOOKS",
        )

    /**
     * For non-partitioned book tables,
     * we can not afford downsampling here because we will lose snapshots
     * (because of the double downsampling: one on the PK, another is here).
     */
    open fun isDownSamplingEnabled(): Boolean = false

    override val s3Databases =
        S3Databases(
            S3Databases.Config(
                tiers = listOf(S3Databases.Config.TierConfig(storageClient = storageClient)),
                customPartitionKeyNames = listOf("exchange", "symbol"),
                timestampPrecision = ChronoUnit.MICROS,
            ),
        )

    override suspend fun hotTierMinAndMaxTime(): Pair<Instant, Instant>? =
        getDbConfig().toDatabase(dbMonitoring).use { db ->
            DerivativesBookDbReaderLegacy(db, marketType.tableNameLegacy).minAndMaxTime()
        }

    override suspend fun convert(
        startDate: LocalDate,
        endDateInclusive: LocalDate,
    ) {
        measureTimeMillis {
            getDbConfig().toDatabase(dbMonitoring).use { db ->
                val theNumberOfParallelDays = db.config.threads
                log.info(
                    "Starting to convert [{}..{}] using {} threads.",
                    startDate,
                    endDateInclusive,
                    theNumberOfParallelDays,
                )

                val dbReader = DerivativesBookDbReaderLegacy(db, marketType.tableNameLegacy)
                val writeConfig =
                    S3Databases
                        .WriteConfig<DerivativesBookDataLegacy, DerivativesBookDataLegacy>(
                            readDispatcher = dispatcher,
                            timeExtractor = { it.time },
                            partitionDownSamplingIntervalResolver = { partitionKey ->
                                if (isDownSamplingEnabled()) {
                                    when (partitionKey.dataType) {
                                        depth100BookDataType.toStr(), depth10PctBookDataType.toStr() ->
                                            Duration.ofSeconds(
                                                10,
                                            )

                                        depthFullBookDataType.toStr() -> Duration.ofHours(1)
                                        else -> error("Unsupported data type ${partitionKey.dataType}")
                                    }
                                } else {
                                    Duration.ofNanos(1)
                                }
                            },
                            partitionKeysResolver = { row ->
                                val exchange =
                                    Resources.getExchangeById(row.exchangeId)?.getNormalizedName()
                                        ?: error("Unsupported exchangeId: ${row.exchangeId}")
                                val symbol = row.symbol
                                val partitionKeyValues = listOf(exchange, symbol)

                                if (row.depthLimit == 100) {
                                    if (row.time >= tenPercentMidPriceBookStartTime) {
                                        tenPercentMidPriceBookBookDataTypes.map {
                                            S3Databases.PartitionKey(
                                                it.toStr(),
                                                partitionKeyValues,
                                            )
                                        }
                                    } else {
                                        listOf(
                                            S3Databases.PartitionKey(
                                                depth100BookDataType.toStr(),
                                                partitionKeyValues,
                                            ),
                                        )
                                    }
                                } else if (row.depthLimit == 30000) {
                                    listOf(S3Databases.PartitionKey(depthFullBookDataType.toStr(), partitionKeyValues))
                                } else {
                                    // ignored
                                    emptyList()
                                }
                            },
                            partitionRowSerializerResolver = { partitionKey ->
                                when (partitionKey.dataType) {
                                    depth100BookDataType.toStr() -> { row ->
                                        if (row.time >= tenPercentMidPriceBookStartTime) {
                                            OrderBookUtils.toJsonBytes(row.limit(100).toMarketOrderBook())
                                        } else {
                                            OrderBookUtils.toJsonBytes(row.toMarketOrderBook())
                                        }
                                    }

                                    depth10PctBookDataType.toStr() -> { row ->
                                        OrderBookUtils.toJsonBytes(row.tenPctMidPrice().toMarketOrderBook())
                                    }

                                    depthFullBookDataType.toStr() -> { row ->
                                        OrderBookUtils.toJsonBytes(row.toMarketOrderBook())
                                    }

                                    else -> error("Unsupported data type ${partitionKey.dataType}")
                                }
                            },
                            partitionKeyPrefixesToFetchStateFrom = emptyList(),
                            expectedBatchSize = 1000,
                            onFileFlushed = { notification ->
                                val bucketName = notification.dataBucketName
                                val exchange = notification.partitionKey.customPartitionKeyValues.first()
                                monitoring.filesFlushed.labels(bucketName, exchange).inc()
                                monitoring.bytesFlushed
                                    .labels(bucketName, exchange)
                                    .inc(notification.fileSize.toDouble())
                            },
                            onFileUploaded = { notification ->
                                val bucketName = notification.dataBucketName
                                val exchange = notification.partitionKey.customPartitionKeyValues.first()
                                monitoring.filesUploaded.labels(bucketName, exchange).inc()
                                monitoring.bytesUploaded
                                    .labels(bucketName, exchange)
                                    .inc(notification.fileSize.toDouble())
                            },
                        )

                val dates = startDate.datesUntil(endDateInclusive.plusDays(1)).toList()

                dates.forEachInParallel(theNumberOfParallelDays) { date ->
                    log.info("Processing date: {}", date)
                    val bookFlow = dbReader.readDay(date)
                    measureTime {
                        s3Databases.writer(writeConfig).write(listOf(bookFlow)).collect()
                    }.also { took ->
                        log.info("Date {} took {}", date, took)
                    }
                }
            }
        }.also { tookMs ->
            log.info("Finished in {}", Duration.ofMillis(tookMs))
        }
    }

    override suspend fun run() {
        val startDate = startDate()
        val endDateInclusive = endDateInclusive()
        convert(startDate, endDateInclusive)
    }

    private fun DerivativesBookDataLegacy.limit(newLimit: Int): DerivativesBookDataLegacy =
        copy(
            asks = asks.take(newLimit),
            bids = bids.take(newLimit),
        )

    private fun DerivativesBookDataLegacy.tenPctMidPrice(): DerivativesBookDataLegacy {
        val result = OrderBookUtils.cutBookTo10PctMidPrice(asks, bids)
        return copy(
            asks = result.first,
            bids = result.second,
        )
    }

    private fun DerivativesBookDataLegacy.toMarketOrderBook(): ApiMarketOrderBook =
        ApiMarketOrderBook(
            time = OrderBookUtils.apiDateTimeFormatter.format(time),
            coinMetricsId =
                exchangeSequenceId?.let { OrderBookUtils.formatBigDecimal(it) } ?: "${
                    ChronoUnit.MICROS.between(
                        Instant.EPOCH,
                        time,
                    )
                }-0",
            asks =
                asks
                    .map {
                        ApiBookEntry(
                            size = OrderBookUtils.formatBigDecimal(it.first),
                            price = OrderBookUtils.formatBigDecimal(it.second),
                        )
                    }.toTypedArray(),
            bids =
                bids
                    .map {
                        ApiBookEntry(
                            size = OrderBookUtils.formatBigDecimal(it.first),
                            price = OrderBookUtils.formatBigDecimal(it.second),
                        )
                    }.toTypedArray(),
            databaseTime = OrderBookUtils.apiDateTimeFormatter.format(databaseTime),
        )
}
