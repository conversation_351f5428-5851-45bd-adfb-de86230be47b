package io.coinmetrics.s3dataloaders.orderbooks.batch.legacy

import io.coinmetrics.databases.DbConfig
import io.coinmetrics.databases.DbMonitoring
import io.coinmetrics.databases.toDatabase
import io.coinmetrics.s3databases.S3Databases
import io.coinmetrics.s3databases.storageclients.StorageClient
import io.coinmetrics.s3dataloaders.orderbooks.ApiBookEntry
import io.coinmetrics.s3dataloaders.orderbooks.ApiMarketOrderBook
import io.coinmetrics.s3dataloaders.orderbooks.BucketNamePrefix
import io.coinmetrics.s3dataloaders.orderbooks.OrderBookUtils
import io.coinmetrics.s3dataloaders.orderbooks.OrderBookUtils.forEachInParallel
import io.coinmetrics.s3dataloaders.orderbooks.Resources
import io.coinmetrics.s3dataloaders.orderbooks.batch.CompositeJob
import io.coinmetrics.s3dataloaders.orderbooks.batch.Monitoring
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collect
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.system.measureTimeMillis
import kotlin.time.measureTime

/**
 * To run locally against cdev1, use:
 * kubectl --context cdev1 -n pgbouncer port-forward svc/pgbouncer 5432
 *
 * Set env variable:
 * DL_DB_BOOKS=postgresql://127.0.0.1:5432/pg-books-stg-hel1-p?user=postgres&password=<password>
 * DL_DB_THREADS=1 (default value)
 * DL_START_DATE=2023-05-20
 *
 * Heap usage per thread depends on numberOfTempFiles * TempFileWriter.bufferSize (4096) and on the DB fetch size
 * DL_HEAP_SIZE=1G (1G per thread is enough)
 * DL_MEM_SIZE=2G
 *
 * in cdev1, we have data only from some previous months
 * select book_time from spot_books order by book_time asc limit 1;
 *
 * The password can be found in Dashlane under the "Cdev1 pgbouncer" item.
 */
@Deprecated("Used for the old tables")
open class SpotBookDbToS3JobLegacy(
    private val monitoring: Monitoring = Monitoring(),
    storageClient: StorageClient,
    private val dispatcher: CoroutineDispatcher = Dispatchers.Default,
    private val tenPercentMidPriceBookStartTime: Instant = OrderBookUtils.tenPercentMidPriceBookStartTime,
) : CompositeJob {
    private val log: Logger = LoggerFactory.getLogger("io.coinmetrics.s3.books.SpotBookDbToS3")
    // private val resolveLogger = ThrottledLogger(LoggerFactory.getLogger(SpotBookDbToS3Job::class.java), throttleMillis = 5000)

    private val dbMonitoring =
        object : DbMonitoring {
            override fun observeExecutionQueueDuration(
                dbName: String,
                durationMs: Long,
            ) {
                // no need to monitor it because we don't have a queue when the number of db threads = the number of app threads
                // log.debug("SQL query spent {} ms in a queue.", durationMs)
            }

            override fun observeQueryDuration(
                dbName: String,
                tableName: String,
                durationMs: Long,
            ) {
                log.trace("SQL query itself took {} ms.", durationMs)
            }

            override fun setExecutionQueueLength(
                dbName: String,
                value: Int,
            ) {
                // no need to monitor it because we don't have a queue when the number of db threads = the number of app threads
                // log.debug("Pending SQL queries in the queue: {}.", value)
            }
        }

    open fun getDbConfig(): DbConfig {
        return DbConfig(
            appName = "DL",
            dbConfigName = "BOOKS",
        )
    }

    open fun getMetadataDbConfig(): DbConfig {
        return DbConfig(
            appName = "DL",
            dbConfigName = "SPOT_METADATA",
        )
    }

    /**
     * For non-partitioned book tables,
     * we can not afford downsampling here because we will lose snapshots
     * (because of the double downsampling: one on the PK, another is here).
     */
    open fun isDownSamplingEnabled(): Boolean = false

    override val s3Databases =
        S3Databases(
            S3Databases.Config(
                tiers = listOf(S3Databases.Config.TierConfig(storageClient = storageClient)),
                customPartitionKeyNames = listOf("exchange", "symbol"),
                timestampPrecision = ChronoUnit.MICROS,
            ),
        )

    override val supportedDataTypes = setOf(BucketNamePrefix.SPOT_100, BucketNamePrefix.SPOT_10PCT, BucketNamePrefix.SPOT_FULL)

    override suspend fun hotTierMinAndMaxTime(): Pair<Instant, Instant>? {
        return getDbConfig().toDatabase(dbMonitoring).use { db ->
            SpotBookDbReaderLegacy(db).minAndMaxTime()
        }
    }

    override suspend fun convert(
        startDate: LocalDate,
        endDateInclusive: LocalDate,
    ) {
        measureTimeMillis {
            getDbConfig().toDatabase(dbMonitoring).use { db ->
                getMetadataDbConfig().toDatabase(dbMonitoring).use { spotMetadataDb ->
                    val theNumberOfParallelDays = db.config.threads
                    log.info(
                        "Starting to convert [{}..{}] using {} threads.",
                        startDate,
                        endDateInclusive,
                        theNumberOfParallelDays,
                    )

                    val dbReader = SpotBookDbReaderLegacy(db)
                    val spotMetadata = CachedSpotMetadata(spotMetadataDb).init()
                    val writeConfig =
                        S3Databases.WriteConfig<SpotBookDbReaderLegacy.SpotBookDataLegacy, SpotBookDbReaderLegacy.SpotBookDataLegacy>(
                            readDispatcher = dispatcher,
                            timeExtractor = { it.time },
                            partitionDownSamplingIntervalResolver = { partitionKey ->
                                if (isDownSamplingEnabled()) {
                                    when (partitionKey.dataType) {
                                        BucketNamePrefix.SPOT_100.toStr(), BucketNamePrefix.SPOT_10PCT.toStr() ->
                                            Duration.ofSeconds(
                                                10,
                                            )
                                        BucketNamePrefix.SPOT_FULL.toStr() -> Duration.ofHours(1)
                                        else -> error("Unsupported data type ${partitionKey.dataType}")
                                    }
                                } else {
                                    Duration.ofNanos(1)
                                }
                            },
                            partitionKeysResolver = { row ->
                                val exchange =
                                    Resources.getExchangeById(row.exchangeId)?.getNormalizedName()
                                        ?: error("Unsupported exchangeId: ${row.exchangeId}")
                                val symbol =
                                    spotMetadata.getSymbol(row.exchangeId, row.baseId, row.quoteId)
//                                            ?: error(
//                                                "Failed to find a symbol for exchangeId=${row.exchangeId}, " +
//                                                    "baseId=${row.baseId}, quoteId=${row.quoteId}.",
//                                            )
                                if (symbol == null) {
                                    monitoring.unresolvedMarkets.labels(
                                        row.exchangeId.toString(),
                                        row.baseId.toString(),
                                        row.quoteId.toString(),
                                    ).inc()
                                    // ignored
                                    emptyList()
                                } else {
                                    val partitionKeyValues = listOf(exchange, symbol)

                                    if (row.depthLimit == 100) {
                                        if (row.time >= tenPercentMidPriceBookStartTime) {
                                            listOf(
                                                S3Databases.PartitionKey(
                                                    BucketNamePrefix.SPOT_100.toStr(),
                                                    partitionKeyValues,
                                                ),
                                                S3Databases.PartitionKey(
                                                    BucketNamePrefix.SPOT_10PCT.toStr(),
                                                    partitionKeyValues,
                                                ),
                                            )
                                        } else {
                                            listOf(
                                                S3Databases.PartitionKey(
                                                    BucketNamePrefix.SPOT_100.toStr(),
                                                    partitionKeyValues,
                                                ),
                                            )
                                        }
                                    } else if (row.depthLimit == 30000) {
                                        listOf(
                                            S3Databases.PartitionKey(
                                                BucketNamePrefix.SPOT_FULL.toStr(),
                                                partitionKeyValues,
                                            ),
                                        )
                                    } else {
                                        // ignored
                                        emptyList()
                                    }
                                }
                            },
                            partitionRowSerializerResolver = { partitionKey ->
                                when (partitionKey.dataType) {
                                    BucketNamePrefix.SPOT_100.toStr() -> { row ->
                                        if (row.time >= tenPercentMidPriceBookStartTime) {
                                            OrderBookUtils.toJsonBytes(row.limit(100).toMarketOrderBook())
                                        } else {
                                            OrderBookUtils.toJsonBytes(row.toMarketOrderBook())
                                        }
                                    }
                                    BucketNamePrefix.SPOT_10PCT.toStr() -> { row ->
                                        OrderBookUtils.toJsonBytes(row.tenPctMidPrice().toMarketOrderBook())
                                    }
                                    BucketNamePrefix.SPOT_FULL.toStr() -> { row ->
                                        OrderBookUtils.toJsonBytes(row.toMarketOrderBook())
                                    }
                                    else -> error("Unsupported data type ${partitionKey.dataType}")
                                }
                            },
                            partitionKeyPrefixesToFetchStateFrom = emptyList(),
                            expectedBatchSize = 1000,
                            onFileFlushed = { notification ->
                                val bucketName = notification.dataBucketName
                                val exchange = notification.partitionKey.customPartitionKeyValues.first()
                                monitoring.filesFlushed.labels(bucketName, exchange).inc()
                                monitoring.bytesFlushed.labels(bucketName, exchange).inc(notification.fileSize.toDouble())
                            },
                            onFileUploaded = { notification ->
                                val bucketName = notification.dataBucketName
                                val exchange = notification.partitionKey.customPartitionKeyValues.first()
                                monitoring.filesUploaded.labels(bucketName, exchange).inc()
                                monitoring.bytesUploaded.labels(bucketName, exchange).inc(notification.fileSize.toDouble())
                            },
                        )

                    val dates = startDate.datesUntil(endDateInclusive.plusDays(1)).toList()
                    dates.forEachInParallel(theNumberOfParallelDays) { date ->
                        log.info("Processing date: {}", date)
                        val spotBookFlow = dbReader.readDay(date)
                        measureTime {
                            s3Databases.writer(writeConfig).write(listOf(spotBookFlow)).collect()
                        }.also { took ->
                            log.info("Date {} took {}", date, took)
                        }
                    }
                }
            }
        }.also { tookMs ->
            log.info("Finished in {}", Duration.ofMillis(tookMs))
        }
    }

    override suspend fun run() {
        val startDate = startDate()
        val endDateInclusive = endDateInclusive()
        convert(startDate, endDateInclusive)
    }

    private fun SpotBookDbReaderLegacy.SpotBookDataLegacy.limit(newLimit: Int): SpotBookDbReaderLegacy.SpotBookDataLegacy {
        return copy(
            asks = asks.take(newLimit),
            bids = bids.take(newLimit),
        )
    }

    private fun SpotBookDbReaderLegacy.SpotBookDataLegacy.tenPctMidPrice(): SpotBookDbReaderLegacy.SpotBookDataLegacy {
        val result = OrderBookUtils.cutBookTo10PctMidPrice(asks, bids)
        return copy(
            asks = result.first,
            bids = result.second,
        )
    }

//    // cache somehow?
//    private fun getMarketEntityId(spotBookData: SpotBookDbReader.SpotBookData, spotMetadata: CachedSpotMetadata): MarketEntityId {
//        val exchange = Resources.getExchangeById(spotBookData.exchangeId)?.getNormalizedName()
//            ?: error("Unsupported exchangeId: ${spotBookData.exchangeId}")
//        val symbol = spotMetadata.getSymbol(spotBookData.exchangeId, spotBookData.baseId, spotBookData.quoteId) ?: error("Failed to find a symbol for exchangeId=${spotBookData.exchangeId}, baseId=${spotBookData.baseId}, quoteId=${spotBookData.quoteId}.")
//        // todo: log instead of throwing an error?
//        //    log.log { error("Failed to find a symbol for exchangeId=${bookData.exchangeId}, baseId=${bookData.baseId}, quoteId=${bookData.quoteId}.") }
//        return MarketEntityId(exchange, symbol)
//    }

    private fun SpotBookDbReaderLegacy.SpotBookDataLegacy.toMarketOrderBook(): ApiMarketOrderBook {
        return ApiMarketOrderBook(
            time = OrderBookUtils.apiDateTimeFormatter.format(time),
            coinMetricsId =
                exchangeSequenceId?.let { OrderBookUtils.formatBigDecimal(it) } ?: "${
                    ChronoUnit.MICROS.between(
                        Instant.EPOCH,
                        time,
                    )}-0",
            asks =
                asks.map {
                    ApiBookEntry(
                        size = OrderBookUtils.formatBigDecimal(it.first),
                        price = OrderBookUtils.formatBigDecimal(it.second),
                    )
                }.toTypedArray(),
            bids =
                bids.map {
                    ApiBookEntry(
                        size = OrderBookUtils.formatBigDecimal(it.first),
                        price = OrderBookUtils.formatBigDecimal(it.second),
                    )
                }.toTypedArray(),
            databaseTime = OrderBookUtils.apiDateTimeFormatter.format(databaseTime),
        )
    }
}
