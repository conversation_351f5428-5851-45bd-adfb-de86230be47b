package io.coinmetrics.s3dataloaders.orderbooks.batch

import io.coinmetrics.healthchecks.BooleanHttpReadyState
import io.coinmetrics.healthchecks.HttpHealthCheck
import io.coinmetrics.s3databases.storageclients.S3StorageClient
import io.coinmetrics.s3dataloaders.orderbooks.batch.legacy.FuturesBookDbToS3JobLegacy
import io.coinmetrics.s3dataloaders.orderbooks.batch.legacy.OptionsBookDbToS3JobLegacy
import io.coinmetrics.s3dataloaders.orderbooks.batch.legacy.OptionsRecalculationJobLegacy
import io.coinmetrics.s3dataloaders.orderbooks.batch.legacy.SpotBookDbToS3JobLegacy
import io.prometheus.client.exporter.HTTPServer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.net.InetSocketAddress
import kotlin.system.exitProcess

/**
 * Main entry point for all jobs.
 */
fun main() {
    val log: Logger = LoggerFactory.getLogger("io.coinmetrics.s3.main")
    val monitoring = Monitoring()
    val healthCheckPort = 8080
    val monitoringPort = 8081
    val monitoringServer = HTTPServer(InetSocketAddress(monitoringPort), monitoring.registry)
    val healthState = BooleanHttpReadyState()
    val healthCheckServer = HttpHealthCheck(port = healthCheckPort, state = healthState)

    val s3config =
        S3StorageClient.S3ClientConfig(
            endpoint = System.getenv("DL_S3_ENDPOINT") ?: error("DL_S3_ENDPOINT is not set"),
            region = System.getenv("DL_S3_REGION") ?: error("DL_S3_REGION is not set"),
            accessKey = System.getenv("DL_S3_ACCESS_KEY") ?: error("DL_S3_ACCESS_KEY is not set"),
            secretKey = System.getenv("DL_S3_SECRET_KEY") ?: error("DL_S3_SECRET_KEY is not set"),
            trustAllCertificates = true,
            onResponseLatencyNotification = { notification ->
                monitoring.s3Latency
                    .labels(
                        notification.s3Method,
                        notification.bucketName,
                    ).observe(notification.latencyMs.toDouble() / 1000)
            },
            maxConcurrency = (System.getenv("DL_S3_MAX_CONCURRENCY") ?: "100").toInt(),
            defaultAcl = System.getenv("DL_S3_DEFAULT_ACL"),
        )
    log.info("Creating S3 Storage Client $s3config")
    val storageClient = S3StorageClient(s3config)
    val dispatcher = monitoring.wrapDispatcherWithMonitoring(Dispatchers.Default, "default")
    val useNewBooksTables = System.getenv("DL_USE_NEW_BOOKS_TABLES")?.toBoolean() ?: false
    log.info("DL_USE_NEW_BOOKS_TABLES is $useNewBooksTables")
    val jobName = System.getenv("DL_JOB") ?: error("DL_JOB is required. Supported jobs: ${JobType.entries}")
    val jobType =
        runCatching { JobType.valueOf(jobName) }
            .onFailure { error("Unsupported job name: $jobName. Supported jobs: ${JobType.entries}") }
            .getOrThrow()

    fun createSpotBookDbToS3Job(): CompositeJob =
        if (useNewBooksTables) {
            SpotBookDbToS3Job(monitoring, storageClient, dispatcher)
        } else {
            SpotBookDbToS3JobLegacy(monitoring, storageClient, dispatcher)
        }

    fun createFuturesBookDbToS3Job(): CompositeJob =
        if (useNewBooksTables) {
            FuturesBookDbToS3Job(monitoring, storageClient, dispatcher)
        } else {
            FuturesBookDbToS3JobLegacy(monitoring, storageClient, dispatcher)
        }

    fun createOptionsBookDbToS3Job(): CompositeJob =
        if (useNewBooksTables) {
            OptionsBookDbToS3Job(monitoring, storageClient, dispatcher)
        } else {
            OptionsBookDbToS3JobLegacy(monitoring, storageClient, dispatcher)
        }

    val job =
        when (jobType) {
            JobType.SpotBookDbToS3Job -> createSpotBookDbToS3Job()

            JobType.FuturesBookDbToS3Job -> createFuturesBookDbToS3Job()

            JobType.OptionsBookDbToS3Job -> createOptionsBookDbToS3Job()

            JobType.OptionsRecalculationJob ->
                if (useNewBooksTables) {
                    error("New tables are not supported for recalculation.")
                } else {
                    OptionsRecalculationJobLegacy(
                        healthCheckServer,
                        monitoring,
                        storageClient,
                        dispatcher,
                    )
                }

            JobType.IndexGeneratorJob -> IndexGeneratorJob(storageClient)
            JobType.RemoveRedundantIndexEntriesJob -> RemoveRedundantIndexEntriesJob(storageClient)
            JobType.RenameFilesJob -> RenameFilesJob(storageClient)
            JobType.SpotBookTierTransitionJob ->
                TierTransitionJob(
                    healthCheckServer,
                    monitoring.registry,
                    createSpotBookDbToS3Job(),
                )

            JobType.FuturesBookTierTransitionJob ->
                TierTransitionJob(
                    healthCheckServer,
                    monitoring.registry,
                    createFuturesBookDbToS3Job(),
                )

            JobType.OptionsBookTierTransitionJob ->
                TierTransitionJob(
                    healthCheckServer,
                    monitoring.registry,
                    createOptionsBookDbToS3Job(),
                )
        }

    runBlocking {
        val currentCoroutineContext = currentCoroutineContext()
        Runtime.getRuntime().addShutdownHook(
            Thread {
                // if (finished) return@Thread
                log.info("Cancelling job {}...", jobName)
                runBlocking {
                    currentCoroutineContext.cancel()
                    storageClient.close()
                    monitoringServer.close()
                    healthCheckServer.close()
                    monitoring.close()
                }
                log.info("Job {} was cancelled.", jobName)
            },
        )

        log.info("Running job {}...", jobName)
        healthState.setState(true)
        try {
            withContext(dispatcher) {
                job.run()
            }
            log.info("Job {} finished.", jobName)
            exitProcess(0)
        } catch (e: Exception) {
            log.error("Job {} failed.", jobName, e)
            exitProcess(1)
        }
    }
}
