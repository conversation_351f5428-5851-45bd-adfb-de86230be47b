package io.coinmetrics.s3dataloaders.orderbooks.batch

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.LocalDate

interface DataLoaderJob {
    private val log: Logger
        get() = LoggerFactory.getLogger(this::class.java)

    fun startDate(): LocalDate {
        var startDate =
            System.getenv("DL_START_DATE")?.let { runCatching { LocalDate.parse(it) }.getOrNull() }
                ?: error("DL_START_DATE must be specified.")

        // shift the start date to distribute the work between jobs
        System.getenv("JOB_COMPLETION_INDEX")?.toLongOrNull()?.also { index ->
            startDate = startDate.plusDays(index)
        }
        return startDate
    }

    fun endDateInclusive(): LocalDate {
        val envEndDate = System.getenv("DL_END_DATE")
        log.info("DL_END_DATE: '$envEndDate'")
        if (envEndDate == null) {
            return startDate()
        }
        return runCatching { LocalDate.parse(envEndDate) }
            .onFailure { e ->
                log.error("Can't parse DL_END_DATE: '$envEndDate'", e)
            }.getOrDefault(startDate())
    }

    /**
     * Suspends until completion. The implementation should be cancellable.
     */
    suspend fun run()
}
